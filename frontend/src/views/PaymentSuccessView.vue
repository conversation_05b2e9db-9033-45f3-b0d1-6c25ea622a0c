<template>
  <div class="container py-5">
    <div class="row justify-content-center">
      <div class="col-md-6">
        <div class="card text-center">
          <div class="card-body">
            <div class="success-icon mb-4">
              <i class="bi bi-check-circle-fill text-success" style="font-size: 4rem;"></i>
            </div>
            <h2 class="text-success mb-3">支付成功！</h2>
            <p class="lead mb-4">
              恭喜您，账户升级已完成。您现在可以享受更多高级功能了！
            </p>
            
            <div class="alert alert-info" role="alert">
              <i class="bi bi-info-circle me-2"></i>
              您的新套餐将在几分钟内生效，请稍候刷新账户页面查看。
            </div>

            <div class="d-grid gap-2 d-md-flex justify-content-md-center">
              <router-link to="/account" class="btn btn-primary btn-lg">
                <i class="bi bi-person-circle me-2"></i>
                查看我的账户
              </router-link>
              <router-link to="/" class="btn btn-outline-primary btn-lg">
                <i class="bi bi-house me-2"></i>
                返回首页
              </router-link>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'

const authStore = useAuthStore()

onMounted(() => {
  // 刷新用户信息以获取最新的订阅状态
  if (authStore.isAuthenticated) {
    authStore.initAuth()
  }
})
</script>

<style scoped>
.success-icon {
  animation: bounceIn 0.6s ease-out;
  color: #0396FF;
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.card {
  box-shadow: 0 4px 12px rgba(3, 150, 255, 0.1);
  border: none;
  transition: transform 0.3s ease;
  background: #FFFFFF;
  position: relative;
  overflow: hidden;
}

.card::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    45deg,
    transparent 0%,
    rgba(3, 150, 255, 0.03) 50%,
    transparent 100%
  );
  animation: shine 3s infinite linear;
  pointer-events: none;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(3, 150, 255, 0.15);
}

.btn-primary {
  background: linear-gradient(135deg, #0396FF 0%, #0D47A1 100%);
  border: none;
  position: relative;
  overflow: hidden;
}

.btn-primary::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    45deg,
    transparent 0%,
    rgba(255, 255, 255, 0.1) 50%,
    transparent 100%
  );
  transform: rotate(45deg);
  animation: shine-button 3s infinite linear;
}

.btn-outline-primary {
  border-color: #0396FF;
  color: #0396FF;
}

.btn-outline-primary:hover {
  background: linear-gradient(135deg, #0396FF 0%, #0D47A1 100%);
  border-color: transparent;
}

.alert-info {
  background-color: rgba(3, 150, 255, 0.1);
  border-color: rgba(3, 150, 255, 0.2);
  color: #0D47A1;
}

@keyframes shine {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes shine-button {
  from {
    transform: translateX(-100%) rotate(45deg);
  }
  to {
    transform: translateX(100%) rotate(45deg);
  }
}

.text-success {
  color: #0396FF !important;
}
</style> 