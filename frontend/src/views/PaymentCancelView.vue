<template>
  <div class="container py-5">
    <div class="row justify-content-center">
      <div class="col-md-6">
        <div class="card text-center">
          <div class="card-body">
            <div class="cancel-icon mb-4">
              <i class="bi bi-x-circle-fill text-warning" style="font-size: 4rem;"></i>
            </div>
            <h2 class="text-warning mb-3">支付已取消</h2>
            <p class="lead mb-4">
              您已取消了支付流程，没有产生任何费用。
            </p>
            
            <div class="alert alert-info" role="alert">
              <i class="bi bi-info-circle me-2"></i>
              如果您遇到任何问题，请联系我们的客服团队获取帮助。
            </div>

            <div class="d-grid gap-2 d-md-flex justify-content-md-center">
              <router-link to="/pricing" class="btn btn-primary btn-lg">
                <i class="bi bi-arrow-left me-2"></i>
                重新选择套餐
              </router-link>
              <router-link to="/" class="btn btn-outline-primary btn-lg">
                <i class="bi bi-house me-2"></i>
                返回首页
              </router-link>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 支付取消页面逻辑
</script>

<style scoped>
.cancel-icon {
  animation: fadeIn 0.6s ease-out;
}

@keyframes fadeIn {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.card {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border: none;
}
</style> 