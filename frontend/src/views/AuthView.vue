<template>
  <div class="container py-5">
    <div class="row justify-content-center">
      <div class="col-md-6">
        <div class="card">
          <div class="card-body">
            <h2 class="text-center mb-4" id="pageTitle">{{ pageTitle }}</h2>
            <p class="text-center text-muted mb-4" id="pageSubtitle">{{ pageSubtitle }}</p>
            
            <form @submit.prevent="handleLogin">
              <div class="mb-3">
                <label for="phone" class="form-label">手机号码</label>
                <div class="input-group">
                  <input
                    type="tel"
                    class="form-control"
                    id="phone"
                    v-model="form.phone"
                    placeholder="请输入手机号"
                    maxlength="11"
                    required
                    @blur="checkUser"
                  />
                  <button
                    type="button"
                    class="btn btn-outline-primary"
                    @click="handleSendCode"
                    :disabled="sendCodeDisabled"
                  >
                    {{ sendCodeText }}
                  </button>
                </div>
              </div>
              
              <div class="mb-3">
                <label for="verification_code" class="form-label">验证码</label>
                <input
                  type="text"
                  class="form-control"
                  id="verification_code"
                  v-model="form.verificationCode"
                  placeholder="请输入验证码"
                  maxlength="6"
                  required
                />
              </div>
              
              <div class="d-grid gap-2">
                <button type="submit" :class="submitButtonClass" :disabled="loading">
                  <span v-if="loading" class="spinner-border spinner-border-sm me-2"></span>
                  {{ submitButtonText }}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { useRouter } from 'vue-router'

const authStore = useAuthStore()
const router = useRouter()

const form = reactive({
  phone: '',
  verificationCode: ''
})

const loading = ref(false)
const userExists = ref(false)
const sendCodeDisabled = ref(false)
const countdown = ref(0)

const pageTitle = computed(() => {
  if (!form.phone) return '手机快捷登录'
  return userExists.value ? '用户登录' : '快速注册'
})

const pageSubtitle = computed(() => {
  if (!form.phone) return '验证手机号即可登录，新用户将自动注册'
  return userExists.value ? '输入验证码登录您的账户' : '新用户将自动创建账户并登录'
})

const submitButtonText = computed(() => {
  if (!form.phone) return '登录/注册'
  return userExists.value ? '登录' : '注册'
})

const submitButtonClass = computed(() => {
  return userExists.value ? 'btn btn-primary' : 'btn btn-success'
})

const sendCodeText = computed(() => {
  return countdown.value > 0 ? `${countdown.value}s` : '发送验证码'
})

// 检查用户是否存在
const checkUser = async () => {
  if (form.phone && /^\d{11}$/.test(form.phone)) {
    try {
      const result = await authStore.checkUserExists(form.phone)
      userExists.value = result.exists
    } catch (error) {
      console.error('检查用户失败:', error)
      userExists.value = false
    }
  } else {
    userExists.value = false
  }
}

// 发送验证码
const handleSendCode = async () => {
  if (!form.phone) {
    alert('请先填写手机号码')
    return
  }
  
  if (!/^\d{11}$/.test(form.phone)) {
    alert('请输入有效的手机号码')
    return
  }

  try {
    await checkUser()
    await authStore.sendCode(form.phone)
    alert('验证码已发送')
    
    // 开始倒计时
    sendCodeDisabled.value = true
    countdown.value = 60
    const timer = setInterval(() => {
      countdown.value--
      if (countdown.value <= 0) {
        clearInterval(timer)
        sendCodeDisabled.value = false
      }
    }, 1000)
  } catch (error: any) {
    alert(error.response?.data?.detail || '发送失败，请稍后重试')
  }
}

// 登录处理
const handleLogin = async () => {
  if (!form.phone || !form.verificationCode) {
    alert('请填写完整信息')
    return
  }

  loading.value = true
  try {
    await authStore.login(form.phone, form.verificationCode)
    const message = userExists.value ? '登录成功！' : '注册成功！欢迎加入！'
    alert(message)
    router.push('/account')
  } catch (error: any) {
    alert(error.response?.data?.detail || '登录失败，请检查验证码')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.card {
  box-shadow: 0 4px 12px rgba(3, 150, 255, 0.1);
  border: none;
  transition: transform 0.3s ease;
  background: #FFFFFF;
  position: relative;
  overflow: hidden;
}

.card::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    45deg,
    transparent 0%,
    rgba(3, 150, 255, 0.03) 50%,
    transparent 100%
  );
  animation: shine 3s infinite linear;
  pointer-events: none;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(3, 150, 255, 0.15);
}

.form-control {
  border: 1px solid rgba(3, 150, 255, 0.2);
  transition: all 0.3s ease;
}

.form-control:focus {
  border-color: #0396FF;
  box-shadow: 0 0 0 0.25rem rgba(3, 150, 255, 0.25);
}

.btn-outline-primary {
  border-color: #0396FF;
  color: #0396FF;
}

.btn-outline-primary:hover {
  background: linear-gradient(135deg, #0396FF 0%, #0D47A1 100%);
  border-color: transparent;
}

.btn-primary {
  background: linear-gradient(135deg, #0396FF 0%, #0D47A1 100%);
  border: none;
  position: relative;
  overflow: hidden;
}

.btn-primary::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    45deg,
    transparent 0%,
    rgba(255, 255, 255, 0.1) 50%,
    transparent 100%
  );
  transform: rotate(45deg);
  animation: shine-button 3s infinite linear;
}

@keyframes shine {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes shine-button {
  from {
    transform: translateX(-100%) rotate(45deg);
  }
  to {
    transform: translateX(100%) rotate(45deg);
  }
}

.input-group .btn {
  border-color: rgba(3, 150, 255, 0.2);
  color: #0396FF;
  background: transparent;
  transition: all 0.3s ease;
}

.input-group .btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #0396FF 0%, #0D47A1 100%);
  border-color: transparent;
  color: white;
}

.input-group .btn:disabled {
  background-color: rgba(3, 150, 255, 0.1);
  border-color: rgba(3, 150, 255, 0.2);
  color: rgba(3, 150, 255, 0.5);
}

#pageTitle {
  background: linear-gradient(135deg, #0396FF 0%, #0D47A1 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}
</style> 