<template>
  <div class="container py-5">
    <div class="text-center mb-5">
      <h1 class="display-4 fw-bold">常见问题</h1>
      <p class="lead text-muted">快速找到您关心问题的答案</p>
    </div>

    <div class="row">
      <div class="col-lg-8 mx-auto">
        <div class="accordion" id="faqAccordion">
          <div class="accordion-item" v-for="(faq, index) in faqs" :key="index">
            <h2 class="accordion-header" :id="`heading${index}`">
              <button
                class="accordion-button"
                :class="{ collapsed: index !== 0 }"
                type="button"
                data-bs-toggle="collapse"
                :data-bs-target="`#collapse${index}`"
                :aria-expanded="index === 0 ? 'true' : 'false'"
                :aria-controls="`collapse${index}`"
              >
                {{ faq.question }}
              </button>
            </h2>
            <div
              :id="`collapse${index}`"
              class="accordion-collapse collapse"
              :class="{ show: index === 0 }"
              :aria-labelledby="`heading${index}`"
              data-bs-parent="#faqAccordion"
            >
              <div class="accordion-body">
                <div v-html="faq.answer"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="row mt-5">
      <div class="col-12 text-center">
        <div class="card">
          <div class="card-body">
            <h5 class="card-title">还有其他问题？</h5>
            <p class="card-text">
              如果您没有找到想要的答案，请联系我们的客服团队，我们将竭诚为您服务。
            </p>
            <div class="d-flex gap-3 justify-content-center">
              <button class="btn btn-primary">
                <i class="bi bi-envelope me-2"></i>邮件联系
              </button>
              <button class="btn btn-outline-primary">
                <i class="bi bi-chat-dots me-2"></i>在线客服
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

interface FAQ {
  question: string
  answer: string
}

const faqs = ref<FAQ[]>([
  {
    question: 'Z-Aiden 是什么？',
    answer: `
      <p>Z-Aiden 是 Zotero 文献管理工具的 AI 助手插件，旨在帮助研究人员更高效地管理和分析文献。它集成了先进的人工智能技术，能够提供智能文献分析、翻译和引用关系查询等服务。</p>
      <p>主要功能包括：</p>
      <ul>
        <li>智能文献综述生成</li>
        <li>多语言文献翻译</li>
        <li>引用关系分析</li>
        <li>概念解释和术语翻译</li>
      </ul>
    `
  },
  {
    question: '如何安装和使用 Z-Aiden？',
    answer: `
      <p>安装和使用 Z-Aiden 非常简单：</p>
      <ol>
        <li>确保您已安装 Zotero 文献管理工具</li>
        <li>从官网下载 Z-Aiden 插件安装包</li>
        <li>在 Zotero 中安装插件（工具 -> 插件 -> 安装插件）</li>
        <li>重启 Zotero 后即可使用</li>
      </ol>
      <p>新用户可以免费使用基础功能，每日有5次查询额度。</p>
    `
  },
  {
    question: 'Z-Aiden 支持哪些语言？',
    answer: `
      <p>Z-Aiden 目前支持以下语言：</p>
      <ul>
        <li>中文</li>
        <li>英文</li>
        <li>日文</li>
        <li>韩文</li>
        <li>德文</li>
        <li>法文</li>
        <li>西班牙文</li>
      </ul>
      <p>我们正在持续添加更多语言支持，以满足不同研究者的需求。</p>
    `
  },
  {
    question: '如何获取更多使用额度？',
    answer: `
      <p>您可以通过以下方式获取更多使用额度：</p>
      <ul>
        <li>升级到高级版或专业版套餐</li>
        <li>参与我们的推荐计划</li>
        <li>完成特定任务获得奖励</li>
      </ul>
      <p>不同套餐的每日使用额度：</p>
      <ul>
        <li>免费版：5次/天</li>
        <li>高级版：100次/天</li>
        <li>专业版：无限次</li>
      </ul>
    `
  },
  {
    question: '如何保证翻译的准确性？',
    answer: `
      <p>Z-Aiden 采用先进的 AI 翻译技术，并结合学术领域专业术语库，确保翻译的准确性：</p>
      <ul>
        <li>使用专业学术语料库进行训练</li>
        <li>支持上下文理解，保持翻译的连贯性</li>
        <li>自动识别并保持专业术语的准确性</li>
        <li>支持用户反馈，持续优化翻译质量</li>
      </ul>
    `
  }
])
</script>

<style scoped>
.faq-section {
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, rgba(3, 150, 255, 0.05) 0%, rgba(3, 150, 255, 0.02) 100%);
  border-radius: 20px;
  padding: 2rem;
  margin-bottom: 2rem;
}

.faq-section::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    45deg,
    transparent 0%,
    rgba(3, 150, 255, 0.03) 50%,
    transparent 100%
  );
  animation: shine 3s infinite linear;
  pointer-events: none;
}

.faq-title {
  background: linear-gradient(135deg, #0396FF 0%, #0D47A1 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 2rem;
}

.accordion-item {
  border: 1px solid rgba(3, 150, 255, 0.1);
  border-radius: 10px;
  margin-bottom: 1rem;
  overflow: hidden;
  transition: all 0.3s ease;
}

.accordion-item:hover {
  box-shadow: 0 4px 12px rgba(3, 150, 255, 0.1);
  transform: translateY(-2px);
}

.accordion-button {
  background: transparent;
  color: #0D47A1;
  font-weight: 500;
  padding: 1.25rem;
  transition: all 0.3s ease;
}

.accordion-button:not(.collapsed) {
  background: linear-gradient(135deg, rgba(3, 150, 255, 0.1) 0%, rgba(3, 150, 255, 0.05) 100%);
  color: #0396FF;
}

.accordion-button:focus {
  box-shadow: none;
  border-color: rgba(3, 150, 255, 0.2);
}

.accordion-button::after {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%230396FF'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
}

.accordion-body {
  padding: 1.25rem;
  color: #666;
  line-height: 1.6;
  background: rgba(3, 150, 255, 0.02);
}

.search-box {
  position: relative;
  margin-bottom: 2rem;
}

.search-input {
  width: 100%;
  padding: 1rem 1.5rem;
  border: 1px solid rgba(3, 150, 255, 0.2);
  border-radius: 10px;
  transition: all 0.3s ease;
  background: white;
}

.search-input:focus {
  outline: none;
  border-color: #0396FF;
  box-shadow: 0 0 0 0.25rem rgba(3, 150, 255, 0.25);
}

.search-icon {
  position: absolute;
  right: 1.5rem;
  top: 50%;
  transform: translateY(-50%);
  color: #0396FF;
}

@keyframes shine {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 响应式优化 */
@media (max-width: 768px) {
  .faq-section {
    padding: 1.5rem;
  }
  
  .accordion-button {
    padding: 1rem;
  }
  
  .accordion-body {
    padding: 1rem;
  }
}
</style> 