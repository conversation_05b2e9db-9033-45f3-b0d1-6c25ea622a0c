<template>
  <div class="container-fluid py-5">
    <div class="container">
      <div class="row">
        <div class="col-12">
          <h1 class="mb-4">我的账户</h1>
        </div>
      </div>

      <div class="row" v-if="userProfile">
        <!-- 用户信息卡片 -->
        <div class="col-lg-6 mb-4">
          <div class="card h-100">
            <div class="card-header">
              <h5 class="mb-0">
                <i class="bi bi-person-circle me-2"></i>个人信息
              </h5>
            </div>
            <div class="card-body">
              <div class="mb-3">
                <strong>手机号：</strong>
                <span>{{ userProfile.user.phone || '未设置' }}</span>
              </div>
              <div class="mb-3">
                <strong>邮箱：</strong>
                <span>{{ userProfile.user.email || '未设置' }}</span>
              </div>
              <div class="mb-3">
                <strong>用户名：</strong>
                <span>{{ userProfile.user.username }}</span>
              </div>
              <div class="mb-0">
                <strong>注册时间：</strong>
                <span>{{ formatDate(userProfile.user.date_joined) }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 订阅信息卡片 -->
        <div class="col-lg-6 mb-4">
          <div class="card h-100">
            <div class="card-header">
              <h5 class="mb-0">
                <i class="bi bi-star-fill me-2"></i>订阅状态
              </h5>
            </div>
            <div class="card-body">
              <div class="mb-3">
                <strong>当前套餐：</strong>
                <span class="badge" :class="getSubscriptionBadgeClass()">
                  {{ getSubscriptionTypeName() }}
                </span>
              </div>
              <div class="mb-3">
                <strong>状态：</strong>
                <span class="badge" :class="userProfile.subscription.is_active ? 'bg-success' : 'bg-danger'">
                  {{ userProfile.subscription.is_active ? '活跃' : '已停用' }}
                </span>
              </div>
              <div class="mb-3" v-if="userProfile.subscription.is_premium">
                <strong>剩余天数：</strong>
                <span>{{ userProfile.subscription.days_remaining }} 天</span>
              </div>
              <div class="mb-0" v-if="userProfile.subscription.end_date">
                <strong>到期时间：</strong>
                <span>{{ formatDate(userProfile.subscription.end_date) }}</span>
              </div>
            </div>
            <div class="card-footer">
              <router-link to="/pricing" class="btn btn-primary w-100">
                <i class="bi bi-arrow-up-circle me-2"></i>升级套餐
              </router-link>
            </div>
          </div>
        </div>
      </div>

      <!-- 使用统计 -->
      <div class="row">
        <div class="col-12">
          <div class="card">
            <div class="card-header">
              <h5 class="mb-0">
                <i class="bi bi-graph-up me-2"></i>使用统计
              </h5>
            </div>
            <div class="card-body">
              <div class="row text-center">
                <div class="col-lg-3 col-md-6 mb-3">
                  <div class="stat-item">
                    <h3 class="text-primary">{{ stats.daily_queries }}</h3>
                    <p class="text-muted mb-0">今日查询</p>
                  </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                  <div class="stat-item">
                    <h3 class="text-info">{{ stats.monthly_queries }}</h3>
                    <p class="text-muted mb-0">本月查询</p>
                  </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                  <div class="stat-item">
                    <h3 class="text-success">{{ getQueryLimit() }}</h3>
                    <p class="text-muted mb-0">每日限额</p>
                  </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                  <div class="stat-item">
                    <h3 class="text-warning">{{ getRemainingQueries() }}</h3>
                    <p class="text-muted mb-0">剩余次数</p>
                  </div>
                </div>
              </div>
              
              <div class="progress mb-3" style="height: 25px;">
                <div
                  class="progress-bar progress-bar-striped"
                  role="progressbar"
                  :style="{ width: getUsagePercentage() + '%' }"
                  :aria-valuenow="getUsagePercentage()"
                  aria-valuemin="0"
                  aria-valuemax="100"
                >
                  {{ getUsagePercentage() }}%
                </div>
              </div>
              <p class="text-center text-muted mb-0">
                今日使用进度
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { apiClient } from '@/services/api'
import type { UserProfile } from '@/types/auth'

const authStore = useAuthStore()
const userProfile = ref<{ user: any; subscription: any } | null>(null)
const stats = ref({
  daily_queries: 15,
  monthly_queries: 342,
})

const getSubscriptionTypeName = () => {
  if (!userProfile.value) return ''
  const typeMap: { [key: string]: string } = {
    'free': '免费版',
    'premium': '高级版',
    'plus': '专业版'
  }
  return typeMap[userProfile.value.subscription.subscription_type] || '未知'
}

const getSubscriptionBadgeClass = () => {
  if (!userProfile.value) return 'bg-secondary'
  const classMap: { [key: string]: string } = {
    'free': 'bg-secondary',
    'premium': 'bg-primary',
    'plus': 'bg-success'
  }
  return classMap[userProfile.value.subscription.subscription_type] || 'bg-secondary'
}

const getQueryLimit = () => {
  if (!userProfile.value) return '0'
  const limitMap: { [key: string]: string } = {
    'free': '5',
    'premium': '100',
    'plus': '无限'
  }
  return limitMap[userProfile.value.subscription.subscription_type] || '0'
}

const getRemainingQueries = () => {
  if (!userProfile.value) return '0'
  
  const type = userProfile.value.subscription.subscription_type
  if (type === 'plus') return '无限'
  
  const limits: { [key: string]: number } = {
    'free': 5,
    'premium': 100
  }
  
  const limit = limits[type] || 0
  const remaining = Math.max(0, limit - stats.value.daily_queries)
  return remaining.toString()
}

const getUsagePercentage = () => {
  if (!userProfile.value) return 0
  
  const type = userProfile.value.subscription.subscription_type
  if (type === 'plus') return 0 // 无限制
  
  const limits: { [key: string]: number } = {
    'free': 5,
    'premium': 100
  }
  
  const limit = limits[type] || 1
  const percentage = Math.min(100, (stats.value.daily_queries / limit) * 100)
  return Math.round(percentage)
}

const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

const loadUserProfile = async () => {
  try {
    const response = await apiClient.get('/user/profile')
    userProfile.value = response.data
  } catch (error: any) {
    console.error('加载用户信息失败:', error)
    let errorMessage = '加载用户信息失败'
    if (error.response) {
      errorMessage += `\n状态码: ${error.response.status}`
      errorMessage += `\n错误信息: ${JSON.stringify(error.response.data)}`
    } else if (error.request) {
      errorMessage += '\n没有收到响应'
    } else {
      errorMessage += `\n${error.message}`
    }
    alert(errorMessage)
  }
}

onMounted(() => {
  loadUserProfile()
})
</script>

<style scoped>
.stat-item {
  padding: 1.5rem;
  border-radius: 12px;
  background: linear-gradient(135deg, rgba(3, 150, 255, 0.05) 0%, rgba(3, 150, 255, 0.02) 100%);
  margin-bottom: 1rem;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-item::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    45deg,
    transparent 0%,
    rgba(3, 150, 255, 0.03) 50%,
    transparent 100%
  );
  animation: shine 3s infinite linear;
  pointer-events: none;
}

.stat-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(3, 150, 255, 0.1);
}

.stat-item h3 {
  margin-bottom: 0.5rem;
  font-weight: bold;
  font-size: 2rem;
  background: linear-gradient(135deg, #0396FF 0%, #0D47A1 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.card {
  box-shadow: 0 4px 12px rgba(3, 150, 255, 0.1);
  border: none;
  transition: transform 0.3s ease;
  background: #FFFFFF;
  position: relative;
  overflow: hidden;
}

.card::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    45deg,
    transparent 0%,
    rgba(3, 150, 255, 0.03) 50%,
    transparent 100%
  );
  animation: shine 3s infinite linear;
  pointer-events: none;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(3, 150, 255, 0.15);
}

.card-header {
  background: linear-gradient(135deg, rgba(3, 150, 255, 0.1) 0%, rgba(3, 150, 255, 0.05) 100%);
  border-bottom: 1px solid rgba(3, 150, 255, 0.1);
  font-weight: 500;
  color: #0D47A1;
}

.progress {
  border-radius: 15px;
  background-color: rgba(3, 150, 255, 0.1);
  overflow: hidden;
}

.progress-bar {
  border-radius: 15px;
  font-weight: 600;
  background: linear-gradient(135deg, #0396FF 0%, #0D47A1 100%);
}

.badge {
  font-size: 0.9rem;
  padding: 0.5rem 1rem;
  background: linear-gradient(135deg, #0396FF 0%, #0D47A1 100%);
}

@keyframes shine {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 响应式优化 */
@media (max-width: 768px) {
  .stat-item {
    padding: 1rem;
  }
  
  .stat-item h3 {
    font-size: 1.5rem;
  }
}
</style> 