<template>
  <div class="container-fluid py-5">
    <div class="container">
      <div class="row">
        <div class="col-12">
          <div class="hero-section text-center py-5">
            <h1 class="display-4 fw-bold mb-4 text-gradient">欢迎使用 Z-Aiden</h1>
            <p class="lead mb-4 text-muted">
              Z-Aiden 是 Zotero 文献管理工具的 AI 助手插件，为您提供智能文献分析、翻译和引用服务。
            </p>
            <div class="d-flex gap-3 justify-content-center flex-wrap">
              <router-link to="/download" class="btn btn-primary btn-lg shine-button">
                <i class="bi bi-download me-2"></i>立即下载
              </router-link>
              <router-link to="/pricing" class="btn btn-outline-primary btn-lg">
                <i class="bi bi-eye me-2"></i>查看定价
              </router-link>
            </div>
          </div>
        </div>
      </div>

      <div class="row mt-5">
        <div class="col-lg-4 col-md-6 mb-4">
          <div class="card h-100 text-center">
            <div class="card-body">
              <i class="bi bi-lightning-charge text-primary fs-1 mb-3"></i>
              <h5 class="card-title">智能文献分析</h5>
              <p class="card-text">
                自动分析文献内容，生成文献综述，帮助您快速理解研究领域。
              </p>
            </div>
          </div>
        </div>
        <div class="col-lg-4 col-md-6 mb-4">
          <div class="card h-100 text-center">
            <div class="card-body">
              <i class="bi bi-shield-check text-success fs-1 mb-3"></i>
              <h5 class="card-title">精准翻译服务</h5>
              <p class="card-text">
                支持多语言文献翻译，保持学术术语的准确性，让跨语言研究更轻松。
              </p>
            </div>
          </div>
        </div>
        <div class="col-lg-4 col-md-6 mb-4">
          <div class="card h-100 text-center">
            <div class="card-body">
              <i class="bi bi-gear text-warning fs-1 mb-3"></i>
              <h5 class="card-title">引用关系分析</h5>
              <p class="card-text">
                智能分析文献间的引用关系，帮助您追踪研究脉络，发现研究热点。
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 首页逻辑（如果需要的话）
</script>

<style scoped>
.hero-section {
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, rgba(3, 150, 255, 0.05) 0%, rgba(3, 150, 255, 0.02) 100%);
  border-radius: 20px;
  padding: 4rem 2rem;
  box-shadow: 0 4px 24px rgba(3, 150, 255, 0.1);
}

.hero-section::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    45deg,
    transparent 0%,
    rgba(3, 150, 255, 0.03) 50%,
    transparent 100%
  );
  animation: shine 3s infinite linear;
  pointer-events: none;
}

@keyframes shine {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.text-gradient {
  background: linear-gradient(135deg, #0396FF 0%, #0D47A1 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.shine-button {
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, #0396FF 0%, #0D47A1 100%);
  border: none;
  transition: all 0.3s ease;
}

.shine-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 16px rgba(3, 150, 255, 0.2);
}

.shine-button::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    45deg,
    transparent 0%,
    rgba(255, 255, 255, 0.1) 50%,
    transparent 100%
  );
  transform: rotate(45deg);
  animation: shine-button 3s infinite linear;
}

@keyframes shine-button {
  from {
    transform: translateX(-100%) rotate(45deg);
  }
  to {
    transform: translateX(100%) rotate(45deg);
  }
}

@media (max-width: 768px) {
  .hero-section {
    padding: 3rem 1rem;
  }
  
  .display-4 {
    font-size: 2.5rem;
  }
}

.card {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* 响应式按钮布局 */
@media (max-width: 768px) {
  .d-flex.gap-3 {
    flex-direction: column;
    gap: 1rem !important;
  }
  
  .btn-lg {
    width: 100%;
  }
}
</style> 