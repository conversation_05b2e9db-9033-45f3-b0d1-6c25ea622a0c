<template>
  <div class="container-fluid my-5">
    <div class="container">
      <div class="text-center mb-5">
        <h1 class="display-4 fw-bold">选择适合您的方案</h1>
        <p class="lead text-muted">升级您的账户，解锁更多强大功能</p>
      </div>
      
      <!-- 免费版和专业版并列 -->
      <div class="row justify-content-center mb-5">
        <!-- 免费版 -->
        <div class="col-xl-5 col-lg-6 col-md-6 mb-4">
          <div class="card pricing-card h-100">
            <div class="pricing-header">
              <div class="text-center">
                <h3 class="fw-bold mb-0">免费版</h3>
                <p class="text-muted mb-0 mt-2" style="color: rgba(255,255,255,0.8) !important;">基础功能，永久免费</p>
                <div class="mt-3">
                  <span class="price">¥0</span>
                  <span class="price-period">/永久</span>
                </div>
              </div>
            </div>
            
            <div class="card-body p-4">
              <ul class="feature-list">
                <li><i class="bi bi-check-circle-fill text-success me-2"></i>每日 5 次 AI 查询</li>
                <li><i class="bi bi-check-circle-fill text-success me-2"></i>基础文献翻译</li>
                <li><i class="bi bi-check-circle-fill text-success me-2"></i>简单概念解释</li>
                <li><i class="bi bi-check-circle-fill text-success me-2"></i>社区支持</li>
              </ul>
            </div>
            
            <div class="card-footer text-center">
              <button class="btn btn-outline-secondary btn-lg w-100" disabled>
                <i class="bi bi-check-circle me-2"></i>当前方案
              </button>
            </div>
          </div>
        </div>

        <!-- 专业版功能展示 -->
        <div class="col-xl-5 col-lg-6 col-md-6 mb-4">
          <div class="card feature-card h-100">
            <div class="card-header text-center">
              <h3 class="fw-bold mb-0">专业版</h3>
              <p class="text-muted mb-0 mt-2">解锁全部功能，享受专业级服务</p>
              <div class="mt-3" style="height: 60px; display: flex; align-items: center; justify-content: center;">
                <span class="feature-highlight">功能全开放</span>
              </div>
            </div>
            <div class="card-body p-4">
              <ul class="feature-list">
                <li><i class="bi bi-check-circle-fill text-success me-2"></i>无限 AI 查询</li>
                <li><i class="bi bi-check-circle-fill text-success me-2"></i>高级文献综述生成</li>
                <li><i class="bi bi-check-circle-fill text-success me-2"></i>专业术语翻译</li>
                <li><i class="bi bi-check-circle-fill text-success me-2"></i>引用关系深度分析</li>
                <li><i class="bi bi-check-circle-fill text-success me-2"></i>批量文献处理</li>
                <li><i class="bi bi-check-circle-fill text-success me-2"></i>优先客服支持</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      <!-- 专业版价格选项 -->
      <div class="row justify-content-center">
        <div class="col-12 text-center mb-4">
          <h4 class="fw-bold">专业版价格方案</h4>
          <p class="text-muted">选择最适合您的付费周期</p>
        </div>
        
        <div class="col-xl-4 col-lg-4 col-md-6 mb-4">
          <div class="card pricing-card position-relative">
            <div class="pricing-header premium">
              <div class="text-center">
                <h4 class="fw-bold mb-0">月付</h4>
                <div class="mt-3">
                  <span class="price">¥39</span>
                  <span class="price-period">/月</span>
                </div>
                <small class="d-block mt-2 opacity-75">按月计费，随时取消</small>
              </div>
            </div>
            
            <div class="card-footer text-center">
              <button 
                class="btn btn-primary btn-lg w-100" 
                @click="handleUpgrade('monthly')"
                :disabled="isLoading"
              >
                <div v-if="isLoading && loadingPlan === 'monthly'" class="d-flex align-items-center justify-content-center">
                  <div class="spinner-border spinner-border-sm me-2" role="status">
                    <span class="visually-hidden">加载中...</span>
                  </div>
                  处理中...
                </div>
                <div v-else>
                  <i class="bi bi-arrow-up-circle me-2"></i>立即升级
                </div>
              </button>
            </div>
          </div>
        </div>

        <div class="col-xl-4 col-lg-4 col-md-6 mb-4">
          <div class="card pricing-card position-relative">
            <div class="popular-badge">推荐</div>
            <div class="pricing-header premium">
              <div class="text-center">
                <h4 class="fw-bold mb-0">半年付</h4>
                <div class="mt-3">
                  <span class="price">¥199</span>
                  <span class="price-period">/6个月</span>
                </div>
                <small class="d-block mt-2 opacity-75">
                  相当于 ¥33/月 
                  <span class="badge bg-success ms-1">省 ¥35</span>
                </small>
              </div>
            </div>
            
            <div class="card-footer text-center">
              <button 
                class="btn btn-primary btn-lg w-100" 
                @click="handleUpgrade('semi_annual')"
                :disabled="isLoading"
              >
                <div v-if="isLoading && loadingPlan === 'semi_annual'" class="d-flex align-items-center justify-content-center">
                  <div class="spinner-border spinner-border-sm me-2" role="status">
                    <span class="visually-hidden">加载中...</span>
                  </div>
                  处理中...
                </div>
                <div v-else>
                  <i class="bi bi-arrow-up-circle me-2"></i>立即升级
                </div>
              </button>
            </div>
          </div>
        </div>

        <div class="col-xl-4 col-lg-4 col-md-6 mb-4">
          <div class="card pricing-card position-relative">
            <div class="pricing-header plus">
              <div class="text-center">
                <h4 class="fw-bold mb-0">年付</h4>
                <div class="mt-3">
                  <span class="price">¥399</span>
                  <span class="price-period">/年</span>
                </div>
                <small class="d-block mt-2 opacity-75">
                  相当于 ¥33/月 
                  <span class="badge bg-success ms-1">省 ¥69</span>
                </small>
              </div>
            </div>
            
            <div class="card-footer text-center">
              <button 
                class="btn btn-success btn-lg w-100" 
                @click="handleUpgrade('annual')"
                :disabled="isLoading"
              >
                <div v-if="isLoading && loadingPlan === 'annual'" class="d-flex align-items-center justify-content-center">
                  <div class="spinner-border spinner-border-sm me-2" role="status">
                    <span class="visually-hidden">加载中...</span>
                  </div>
                  处理中...
                </div>
                <div v-else>
                  <i class="bi bi-arrow-up-circle me-2"></i>立即升级
                </div>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 错误提示 -->
      <div v-if="errorMessage" class="row justify-content-center mt-4">
        <div class="col-md-8">
          <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle me-2"></i>
            {{ errorMessage }}
            <button type="button" class="btn-close" @click="clearError" aria-label="Close"></button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { PaymentService } from '@/services/payment'

const router = useRouter()
const authStore = useAuthStore()
const isLoading = ref(false)
const loadingPlan = ref<string | null>(null)
const errorMessage = ref<string | null>(null)

const handleUpgrade = async (planId: string) => {
  // 检查是否已登录
  if (!authStore.isAuthenticated) {
    // 跳转到登录页面，并在URL中带上回调参数
    router.push('/auth?mode=login&redirect=/pricing')
    return
  }

  isLoading.value = true
  loadingPlan.value = planId
  errorMessage.value = null

  try {
    const planType = PaymentService.getPlanType(planId)
    await PaymentService.startPayment(planType)
  } catch (error: any) {
    console.error('启动支付失败:', error)
    errorMessage.value = error.message || '启动支付失败，请稍后重试'
  } finally {
    isLoading.value = false
    loadingPlan.value = null
  }
}

const clearError = () => {
  errorMessage.value = null
}
</script>

<style scoped>
.pricing-card {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border: none;
  box-shadow: 0 4px 12px rgba(3, 150, 255, 0.1);
  background: #FFFFFF;
  position: relative;
  overflow: hidden;
  border-radius: 15px;
}

.feature-card {
  border: none;
  box-shadow: 0 4px 12px rgba(3, 150, 255, 0.1);
  background: linear-gradient(135deg, rgba(3, 150, 255, 0.05) 0%, rgba(3, 150, 255, 0.02) 100%);
  border-radius: 15px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 20px rgba(3, 150, 255, 0.15);
}

.pricing-card::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    45deg,
    transparent 0%,
    rgba(3, 150, 255, 0.03) 50%,
    transparent 100%
  );
  animation: shine 3s infinite linear;
  pointer-events: none;
}

@keyframes shine {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.pricing-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(3, 150, 255, 0.15);
}

.pricing-header {
  background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
  color: white;
  padding: 1.5rem;
  border-radius: 15px 15px 0 0;
  min-height: 140px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.pricing-header.premium {
  background: linear-gradient(135deg, #0396FF 0%, #0061FF 100%);
}

.pricing-header.plus {
  background: linear-gradient(135deg, #198754 0%, #146c43 100%);
}

.card-header {
  background: linear-gradient(135deg, rgba(3, 150, 255, 0.1) 0%, rgba(3, 150, 255, 0.05) 100%);
  border-bottom: 1px solid rgba(3, 150, 255, 0.1);
  border-radius: 15px 15px 0 0 !important;
  padding: 1.5rem;
  min-height: 140px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.price {
  font-size: 2.5rem;
  font-weight: bold;
  margin: 0;
}

.price-period {
  font-size: 1rem;
  opacity: 0.8;
}

.feature-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.feature-list li {
  padding: 0.5rem 0;
  transition: background-color 0.3s ease;
}

.feature-list li:hover {
  background-color: rgba(3, 150, 255, 0.03);
}

.popular-badge {
  position: absolute;
  top: -1px;
  right: 20px;
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  color: white;
  padding: 0.5rem 1.5rem;
  border-radius: 0 0 15px 15px;
  font-size: 0.875rem;
  font-weight: bold;
  z-index: 10;
  box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3);
}

.card-footer {
  background: transparent;
  border-top: 1px solid rgba(3, 150, 255, 0.1);
  padding: 1.5rem;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .pricing-header {
    padding: 1.5rem 1rem;
  }
  
  .price {
    font-size: 2rem;
  }
  
  .popular-badge {
    right: 15px;
    padding: 0.4rem 1rem;
    font-size: 0.8rem;
  }
  
  .feature-list li {
    padding: 0.4rem 0;
  }
}

@media (max-width: 576px) {
  .container-fluid {
    padding: 0 15px;
  }
  
  .pricing-card {
    margin-bottom: 2rem;
  }
  
  .col-xl-4, .col-xl-5 {
    max-width: 100%;
  }
}

/* 确保三列在大屏幕上并排显示 */
@media (min-width: 992px) {
  .row .col-xl-4 {
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
  }
  
  .row .col-xl-5 {
    flex: 0 0 41.666667%;
    max-width: 41.666667%;
  }
}

/* 确保免费版和专业版高度一致 */
.h-100 {
  height: 100% !important;
}

.feature-highlight {
  background: linear-gradient(135deg, #0396FF 0%, #0061FF 100%);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
}
</style> 