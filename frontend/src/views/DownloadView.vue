<template>
  <div class="container py-5">
    <div class="text-center mb-5">
      <h1 class="display-4 fw-bold">下载安装</h1>
      <p class="lead text-muted">简单几步，开始使用 Z-Aiden</p>
    </div>

    <div class="row justify-content-center">
      <div class="col-lg-8">
        <!-- 系统要求 -->
        <div class="card mb-4">
          <div class="card-header">
            <h5 class="mb-0">
              <i class="bi bi-info-circle me-2"></i>系统要求
            </h5>
          </div>
          <div class="card-body">
            <ul class="list-unstyled">
              <li class="mb-2">
                <i class="bi bi-check-circle-fill text-success me-2"></i>
                Zotero 7.0 或更高版本
              </li>
            </ul>
          </div>
        </div>

        <!-- 下载链接 -->
        <div class="card mb-4">
          <div class="card-header">
            <h5 class="mb-0">
              <i class="bi bi-download me-2"></i>下载插件
            </h5>
          </div>
          <div class="card-body">
            <div class="d-grid gap-3">
              <button class="btn btn-primary btn-lg" @click="showComingSoon">
                <i class="bi bi-download me-2"></i>下载最新版本
                <small class="d-block">v1.0.0</small>
              </button>
            </div>
          </div>
        </div>

        <!-- 安装步骤 -->
        <div class="card mb-4">
          <div class="card-header">
            <h5 class="mb-0">
              <i class="bi bi-gear me-2"></i>安装步骤
            </h5>
          </div>
          <div class="card-body">
            <div class="steps">
              <div class="step mb-4">
                <div class="step-number">1</div>
                <div class="step-content">
                  <h6>下载插件</h6>
                  <p>点击上方按钮下载最新版本的 Z-Aiden 插件。</p>
                </div>
              </div>
              <div class="step mb-4">
                <div class="step-number">2</div>
                <div class="step-content">
                  <h6>安装插件</h6>
                  <p>在 Zotero 中，点击"工具" -> "插件" -> "安装插件"，选择下载的 .xpi 文件。</p>
                </div>
              </div>
              <div class="step mb-4">
                <div class="step-number">3</div>
                <div class="step-content">
                  <h6>重启 Zotero</h6>
                  <p>安装完成后，重启 Zotero 以激活插件。</p>
                </div>
              </div>
              <div class="step">
                <div class="step-number">4</div>
                <div class="step-content">
                  <h6>开始使用</h6>
                  <p>在 Zotero 中，您可以通过右键菜单或工具栏使用 Z-Aiden 的功能。</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 常见问题 -->
        <div class="card">
          <div class="card-header">
            <h5 class="mb-0">
              <i class="bi bi-question-circle me-2"></i>安装问题
            </h5>
          </div>
          <div class="card-body">
            <div class="accordion" id="installFaqAccordion">
              <div class="accordion-item">
                <h2 class="accordion-header">
                  <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#installFaq1">
                    安装时提示"无法安装此插件"怎么办？
                  </button>
                </h2>
                <div id="installFaq1" class="accordion-collapse collapse" data-bs-parent="#installFaqAccordion">
                  <div class="accordion-body">
                    <p>请确保：</p>
                    <ul>
                      <li>您使用的是最新版本的 Zotero</li>
                      <li>下载的插件文件完整且未损坏</li>
                      <li>您有足够的系统权限</li>
                    </ul>
                    <p>如果问题仍然存在，请尝试重启 Zotero 后再次安装。</p>
                  </div>
                </div>
              </div>
              <div class="accordion-item">
                <h2 class="accordion-header">
                  <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#installFaq2">
                    如何更新插件？
                  </button>
                </h2>
                <div id="installFaq2" class="accordion-collapse collapse" data-bs-parent="#installFaqAccordion">
                  <div class="accordion-body">
                    <p>Z-Aiden 会自动检查更新。当有新版本时，您会收到更新提示。您也可以：</p>
                    <ol>
                      <li>在 Zotero 中点击"工具" -> "插件"</li>
                      <li>找到 Z-Aiden 插件</li>
                      <li>点击"检查更新"按钮</li>
                    </ol>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { showComingSoon } from '@/utils/comingSoon'
// 下载页面逻辑（如果需要的话）
</script>

<style scoped>
.step {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.step-number {
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, #0396FF 0%, #0D47A1 100%);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  flex-shrink: 0;
}

.step-content {
  flex-grow: 1;
}

.step-content h6 {
  margin-bottom: 0.5rem;
  color: #333;
}

.step-content p {
  margin-bottom: 0;
  color: #666;
}

.card {
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn-lg {
  padding: 1rem;
}

.btn-lg small {
  font-size: 0.875rem;
  opacity: 0.8;
}

.accordion-button:not(.collapsed) {
  background-color: rgba(3, 150, 255, 0.1);
  color: #0396FF;
}

.accordion-button:focus {
  box-shadow: none;
  border-color: rgba(3, 150, 255, 0.25);
}
</style> 