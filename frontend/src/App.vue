<script setup lang="ts">
import { onMounted, ref, onUnmounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { useRouter } from 'vue-router'
import NavBar from '@/components/NavBar.vue'

const authStore = useAuthStore()
const router = useRouter()
const showDropdown = ref(false)
const dropdownRef = ref<HTMLElement | null>(null)

const logout = () => {
  authStore.logout()
  router.push('/')
  showDropdown.value = false
}

const toggleDropdown = (event: Event) => {
  event.stopPropagation()
  showDropdown.value = !showDropdown.value
}

const handleClickOutside = (event: MouseEvent) => {
  if (dropdownRef.value && !dropdownRef.value.contains(event.target as Node)) {
    showDropdown.value = false
  }
}

onMounted(() => {
  authStore.initAuth()
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<template>
  <NavBar />
  <router-view />
</template>

<style>
@import 'bootstrap/dist/css/bootstrap.min.css';
@import 'bootstrap-icons/font/bootstrap-icons.css';

body {
  background-color: #f8f9fa;
}

#app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.router-view {
  flex: 1;
}

main {
  flex: 1;
}

.footer {
  margin-top: auto;
  background: linear-gradient(135deg, rgba(3, 150, 255, 0.05) 0%, rgba(3, 150, 255, 0.02) 100%);
}

.navbar {
  background: white;
  box-shadow: 0 2px 10px rgba(3, 150, 255, 0.1);
  padding: 1rem;
}

.navbar-brand {
  font-weight: bold;
  font-size: 1.5rem;
  background: linear-gradient(135deg, #0396FF 0%, #0D47A1 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.nav-link {
  color: #0D47A1;
  font-weight: 500;
  padding: 0.5rem 1rem;
  transition: all 0.3s ease;
  border-radius: 8px;
}

.nav-link:hover {
  color: #0396FF;
  background: rgba(3, 150, 255, 0.05);
}

.login-btn {
  background: linear-gradient(135deg, #0396FF 0%, #0D47A1 100%);
  color: white !important;
  border-radius: 20px;
  padding: 0.5rem 1.5rem;
}

.login-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(3, 150, 255, 0.2);
  background: linear-gradient(135deg, #0396FF 0%, #0D47A1 100%);
  color: white !important;
}

.user-menu-btn {
  background: transparent;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  font-weight: 500;
  padding: 0.5rem 1rem;
}

.dropdown {
  position: relative;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  z-index: 1000;
  display: none;
  min-width: 10rem;
  padding: 0.5rem 0;
  margin: 0.5rem 0 0;
  background-color: white;
  border: 1px solid rgba(3, 150, 255, 0.1);
  border-radius: 10px;
  box-shadow: 0 4px 16px rgba(3, 150, 255, 0.1);
  animation: fadeIn 0.2s ease-out;
}

.dropdown-menu.show {
  display: block;
}

.dropdown-item {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  color: #0D47A1;
  transition: all 0.3s ease;
}

.dropdown-item:hover {
  background: rgba(3, 150, 255, 0.05);
  color: #0396FF;
}

.dropdown-divider {
  height: 1px;
  background: rgba(3, 150, 255, 0.1);
  margin: 0.5rem 0;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 确保响应式导航栏的样式 */
@media (max-width: 991.98px) {
  .navbar-nav {
    text-align: center;
  }
  
  .nav-item {
    margin: 0.25rem 0;
  }

  .dropdown-menu {
    position: static;
    float: none;
    width: auto;
    margin-top: 0;
    box-shadow: none;
    border: none;
    background: rgba(3, 150, 255, 0.05);
  }
}
</style>
