/**
 * 支付相关工具函数
 */

/**
 * 获取套餐信息
 */
export const getPlanInfo = (planId: string) => {
  const plans = {
    monthly: {
      name: '专业版月付',
      price: 39,
      period: '月',
      description: '按月计费，随时取消',
      type: 'premium' as const
    },
    semi_annual: {
      name: '专业版半年付',
      price: 199,
      period: '6个月',
      description: '相当于 ¥33/月，省 ¥35',
      type: 'premium' as const
    },
    annual: {
      name: '专业版年付',
      price: 399,
      period: '年',
      description: '相当于 ¥33/月，省 ¥69',
      type: 'plus' as const
    }
  }
  
  return plans[planId as keyof typeof plans]
}

/**
 * 格式化价格显示
 */
export const formatPrice = (price: number): string => {
  return `¥${price.toFixed(0)}`
}

/**
 * 验证支付结果
 */
export const validatePaymentResult = (result: any): boolean => {
  return result && result.success && result.payment_url
}

/**
 * 处理支付错误
 */
export const handlePaymentError = (error: any): string => {
  if (typeof error === 'string') {
    return error
  }
  
  if (error?.response?.data?.message) {
    return error.response.data.message
  }
  
  if (error?.message) {
    return error.message
  }
  
  return '启动支付失败，请稍后重试'
} 