import { apiClient } from './api'
import type { CreatePaymentRequest, PaymentResponse } from '@/types/payment'

export class PaymentService {
  /**
   * 创建支付
   */
  static async createPayment(planType: 'premium' | 'plus'): Promise<PaymentResponse> {
    try {
      const response = await apiClient.post<PaymentResponse>('/payment/create', {
        plan_type: planType
      })
      return response.data
    } catch (error: any) {
      console.error('创建支付失败:', error)
      return {
        success: false,
        error: error.response?.data?.message || '创建支付失败，请稍后重试'
      }
    }
  }

  /**
   * 启动支付流程
   */
  static async startPayment(planType: 'premium' | 'plus'): Promise<void> {
    try {
      const result = await this.createPayment(planType)
      
      if (result.success && result.payment_url) {
        // 跳转到 Antom 支付页面
        window.location.href = result.payment_url
      } else {
        throw new Error(result.error || '创建支付失败')
      }
    } catch (error: any) {
      console.error('启动支付失败:', error)
      throw error
    }
  }

  /**
   * 根据计划ID获取支付类型
   */
  static getPlanType(planId: string): 'premium' | 'plus' {
    // 根据前端的计划ID映射到后端的支付类型
    switch (planId) {
      case 'monthly':
      case 'semi_annual':
        return 'premium'
      case 'annual':
        return 'plus'
      default:
        return 'premium'
    }
  }
} 