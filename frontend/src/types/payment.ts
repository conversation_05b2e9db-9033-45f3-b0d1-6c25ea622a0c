export interface CreatePaymentRequest {
  plan_type: 'premium' | 'plus'
}

export interface PaymentResponse {
  success: boolean
  payment_url?: string
  error?: string
}

export interface PaymentPlan {
  id: string
  name: string
  price: number
  period: string
  description: string
  features: string[]
  popular?: boolean
}

export const PAYMENT_PLANS: PaymentPlan[] = [
  {
    id: 'monthly',
    name: '月付',
    price: 39,
    period: '月',
    description: '按月计费，随时取消',
    features: ['无限 AI 查询', '高级文献综述生成', '专业术语翻译', '引用关系深度分析', '批量文献处理', '优先客服支持']
  },
  {
    id: 'semi_annual',
    name: '半年付',
    price: 199,
    period: '6个月',
    description: '相当于 ¥33/月',
    features: ['无限 AI 查询', '高级文献综述生成', '专业术语翻译', '引用关系深度分析', '批量文献处理', '优先客服支持'],
    popular: true
  },
  {
    id: 'annual',
    name: '年付',
    price: 399,
    period: '年',
    description: '相当于 ¥33/月',
    features: ['无限 AI 查询', '高级文献综述生成', '专业术语翻译', '引用关系深度分析', '批量文献处理', '优先客服支持']
  }
] 