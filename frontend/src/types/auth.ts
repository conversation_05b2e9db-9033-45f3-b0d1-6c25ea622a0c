export interface User {
  id: number
  phone?: string
  email?: string
  username: string
  date_joined: string
}

export interface Subscription {
  subscription_type: string
  start_date: string
  end_date?: string
  is_active: boolean
  is_premium: boolean
  days_remaining: number
}

export interface UserProfile extends User {
  subscription: Subscription
}

export interface LoginResponse {
  access_token: string
  refresh_token: string
  token_type: string
  expires_in: number
  user: User
}

export interface MessageResponse {
  status: string
  message: string
}

export interface UserExistsResponse {
  exists: boolean
} 