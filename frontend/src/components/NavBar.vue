<template>
  <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
    <div class="container">
      <router-link class="navbar-brand" to="/">
        <span class="text-gradient">Z-<PERSON></span>
      </router-link>
      
      <button
        class="navbar-toggler"
        type="button"
        data-bs-toggle="collapse"
        data-bs-target="#navbarNav"
        aria-controls="navbarNav"
        aria-expanded="false"
        aria-label="Toggle navigation"
      >
        <span class="navbar-toggler-icon"></span>
      </button>
      
      <div class="collapse navbar-collapse" id="navbarNav">
        <ul class="navbar-nav me-auto">
          <li class="nav-item">
            <router-link class="nav-link" to="/">首页</router-link>
          </li>
          <li class="nav-item">
            <router-link class="nav-link" to="/download">下载安装</router-link>
          </li>
          <li class="nav-item">
            <router-link class="nav-link" to="/pricing">定价</router-link>
          </li>
          <li class="nav-item">
            <router-link class="nav-link" to="/faq">常见问题</router-link>
          </li>
        </ul>
        
        <div class="d-flex">
          <template v-if="false">
            <button class="btn btn-outline-primary me-2" @click="showComingSoon">
              <i class="bi bi-person-circle me-1"></i>我的账户
            </button>
            <button class="btn btn-primary" @click="showComingSoon">
              <i class="bi bi-box-arrow-right me-1"></i>退出
            </button>
          </template>
          <template v-else>
            <button class="btn btn-primary" @click="showComingSoon">
              <i class="bi bi-box-arrow-in-right me-1"></i>登录/注册
            </button>
          </template>
        </div>
      </div>
    </div>
  </nav>
</template>

<script setup lang="ts">
import { useAuthStore } from '@/stores/auth'
import { useRouter } from 'vue-router'
import { showComingSoon } from '@/utils/comingSoon'

const authStore = useAuthStore()
const router = useRouter()

const handleLogout = async () => {
  try {
    await authStore.logout()
    router.push('/')
  } catch (error) {
    console.error('退出失败:', error)
  }
}
</script>

<style scoped>
.navbar {
  position: sticky;
  top: 0;
  z-index: 1000;
}

.text-gradient {
  background: linear-gradient(135deg, #0396FF 0%, #0D47A1 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: bold;
}

.nav-link {
  color: #333;
  font-weight: 500;
  padding: 0.5rem 1rem;
  transition: color 0.3s ease;
}

.nav-link:hover {
  color: #0396FF;
}

.nav-link.router-link-active {
  color: #0396FF;
}

.btn {
  padding: 0.5rem 1rem;
  font-weight: 500;
}

.btn-link {
  color: #333;
  text-decoration: none;
  border: none;
  background: none;
  padding: 0.5rem 1rem;
  font-weight: 500;
  transition: color 0.3s ease;
}

.btn-link:hover {
  color: #0396FF;
  text-decoration: none;
}

@media (max-width: 991.98px) {
  .navbar-collapse {
    padding: 1rem 0;
  }
  
  .d-flex {
    margin-top: 1rem;
  }
  
  .btn-link {
    text-align: left;
  }
}
</style> 